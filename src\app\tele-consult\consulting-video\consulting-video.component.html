<!-- OpenVidu Session Component -->
<opv-session
  #ovSessionComponent
  [sessionName]="mySessionId"
  [user]="myUserName"
  [tokens]="tokens"
  [ovSettings]="ovSettings"
  (sessionCreated)="handlerSessionCreatedEvent($event)"
  (participantCreated)="handlerPublisherCreatedEvent($event)"
  (error)="handlerErrorEvent($event)"
  *ngIf="session && sessionJoined">
</opv-session>

<!-- Custom Multi-Camera Stream Display -->
<div id="multi-video-container" class="multi-camera-grid" *ngIf="videoDevices.length > 1">
  <!-- Video elements will be appended here dynamically in publishMultipleStreams() -->
</div>
